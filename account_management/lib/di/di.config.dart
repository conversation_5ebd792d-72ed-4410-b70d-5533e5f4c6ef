// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:account_management/application/account_management_bloc/account_management_bloc.dart'
    as _i357;
import 'package:account_management/application/account_watcher_bloc/account_watcher_bloc.dart'
    as _i1051;
import 'package:account_management/application/daily_medication_bloc/daily_medication_bloc.dart'
    as _i692;
import 'package:account_management/application/manage_medications_bloc/manage_medications_bloc.dart'
    as _i270;
import 'package:account_management/application/manage_ovulation_bloc/manage_ovulation_bloc.dart'
    as _i500;
import 'package:account_management/application/manage_period_tracking_bloc/manage_period_tracking_bloc.dart'
    as _i703;
import 'package:account_management/application/medication_form_bloc/medication_form_bloc.dart'
    as _i644;
import 'package:account_management/application/medication_watcher_bloc/medication_watcher_bloc.dart'
    as _i957;
import 'package:account_management/application/menstrual_cycle_bloc/menstrual_cycle_bloc.dart'
    as _i880;
import 'package:account_management/application/onboardin_form_bloc/onboarding_form_bloc.dart'
    as _i712;
import 'package:account_management/application/period_reminder_settings_bloc/period_reminder_settings_bloc.dart'
    as _i1044;
import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart'
    as _i689;
import 'package:account_management/application/symptom_tracking_bloc/symptom_tracking_bloc.dart'
    as _i792;
import 'package:account_management/application/symptom_watcher_bloc/symptom_watcher_bloc.dart'
    as _i993;
import 'package:account_management/application/update_health_data_bloc/update_health_data_bloc.dart'
    as _i1038;
import 'package:account_management/domain/facade/account_management_facade.dart'
    as _i380;
import 'package:account_management/domain/facade/health_data_facade.dart'
    as _i698;
import 'package:account_management/domain/facade/medication_facade.dart'
    as _i717;
import 'package:account_management/domain/facade/mestrual_cycle_facade.dart'
    as _i923;
import 'package:account_management/domain/facade/ovulation_facade.dart'
    as _i805;
import 'package:account_management/domain/facade/period_reminder_facade.dart'
    as _i872;
import 'package:account_management/domain/facade/period_tracking_facade.dart'
    as _i258;
import 'package:account_management/domain/facade/symptom_management_facade.dart'
    as _i538;
import 'package:account_management/domain/facade/symptom_tracking_facade.dart'
    as _i830;
import 'package:account_management/infrastructure/services/firestore_service.dart'
    as _i328;
import 'package:account_management/infrastructure/services/ovulation_service.dart'
    as _i479;
import 'package:account_management/infrastructure/services/period_data_service.dart'
    as _i259;
import 'package:account_management/infrastructure/services/prediction_service.dart'
    as _i877;
import 'package:account_management/infrastructure/services/symptom_service.dart'
    as _i550;
import 'package:account_management/repository/account_management_repository.dart'
    as _i795;
import 'package:account_management/repository/health_data_repository.dart'
    as _i56;
import 'package:account_management/repository/medication_repository.dart'
    as _i467;
import 'package:account_management/repository/mestrual_cycle_repository.dart'
    as _i1027;
import 'package:account_management/repository/ovulation_repository.dart'
    as _i282;
import 'package:account_management/repository/period_reminder_repository.dart'
    as _i541;
import 'package:account_management/repository/period_tracking_repository.dart'
    as _i201;
import 'package:account_management/repository/symptom_management_repository.dart'
    as _i400;
import 'package:account_management/repository/symptom_tracking_repository.dart'
    as _i731;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:notifications/domain/facade/scheduled_notifications_facade.dart'
    as _i828;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    gh.factory<_i328.FirestoreService>(() => _i328.FirestoreService());
    gh.factory<_i550.SymptomService>(
        () => _i550.SymptomService(gh<_i328.FirestoreService>()));
    gh.factory<_i259.PeriodDataService>(
        () => _i259.PeriodDataService(gh<_i328.FirestoreService>()));
    gh.lazySingleton<_i698.HealthDataFacade>(() => _i56.HealthDataRepository());
    gh.lazySingleton<_i872.PeriodReminderFacade>(() =>
        _i541.PeriodReminderRepository(
            gh<_i828.ScheduledNotificationsFacade>()));
    gh.lazySingleton<_i717.MedicationFacade>(
        () => _i467.MedicationRepository());
    gh.lazySingleton<_i923.MenstrualCycleFacade>(
        () => _i1027.MestrualCycleRepository());
    gh.factory<_i270.ManageMedicationsBloc>(
        () => _i270.ManageMedicationsBloc(gh<_i717.MedicationFacade>()));
    gh.factory<_i692.DailyMedicationBloc>(
        () => _i692.DailyMedicationBloc(gh<_i717.MedicationFacade>()));
    gh.factory<_i957.MedicationWatcherBloc>(
        () => _i957.MedicationWatcherBloc(gh<_i717.MedicationFacade>()));
    gh.factory<_i644.MedicationFormBloc>(
        () => _i644.MedicationFormBloc(gh<_i717.MedicationFacade>()));
    gh.lazySingleton<_i805.OvulationFacade>(() => _i282.OvulationRepository(
          gh<_i259.PeriodDataService>(),
          gh<_i328.FirestoreService>(),
          gh<_i698.HealthDataFacade>(),
        ));
    gh.lazySingleton<_i538.SymptomManagementFacade>(
        () => _i400.SymptomManagementRepository());
    gh.factory<_i880.MenstrualCycleBloc>(
        () => _i880.MenstrualCycleBloc(gh<_i923.MenstrualCycleFacade>()));
    gh.factory<_i1044.PeriodReminderSettingsBloc>(() =>
        _i1044.PeriodReminderSettingsBloc(gh<_i872.PeriodReminderFacade>()));
    gh.factory<_i500.ManageOvulationBloc>(
        () => _i500.ManageOvulationBloc(gh<_i805.OvulationFacade>()));
    gh.factory<_i993.SymptomWatcherBloc>(
        () => _i993.SymptomWatcherBloc(gh<_i538.SymptomManagementFacade>()));
    gh.factory<_i877.PredictionService>(() => _i877.PredictionService(
          gh<_i698.HealthDataFacade>(),
          gh<_i259.PeriodDataService>(),
        ));
    gh.factory<_i1038.UpdateHealthDataBloc>(
        () => _i1038.UpdateHealthDataBloc(gh<_i698.HealthDataFacade>()));
    gh.factory<_i479.OvulationService>(() => _i479.OvulationService(
          gh<_i328.FirestoreService>(),
          gh<_i259.PeriodDataService>(),
          gh<_i698.HealthDataFacade>(),
        ));
    gh.lazySingleton<_i258.PeriodTrackingFacade>(
        () => _i201.PeriodTrackingRepository(
              gh<_i328.FirestoreService>(),
              gh<_i259.PeriodDataService>(),
              gh<_i877.PredictionService>(),
              gh<_i698.HealthDataFacade>(),
              gh<_i805.OvulationFacade>(),
            ));
    gh.factory<_i689.PeriodTrackingWatcherBloc>(() =>
        _i689.PeriodTrackingWatcherBloc(gh<_i258.PeriodTrackingFacade>()));
    gh.factory<_i703.ManagePeriodTrackingBloc>(
        () => _i703.ManagePeriodTrackingBloc(gh<_i258.PeriodTrackingFacade>()));
    gh.lazySingleton<_i380.AccountManagementFacade>(
        () => _i795.AccountManagementRepository(
              gh<_i258.PeriodTrackingFacade>(),
              gh<_i805.OvulationFacade>(),
            ));
    gh.lazySingleton<_i830.SymptomTrackingFacade>(
        () => _i731.SymptomTrackingRepository(
              gh<_i328.FirestoreService>(),
              gh<_i258.PeriodTrackingFacade>(),
              gh<_i259.PeriodDataService>(),
            ));
    gh.factory<_i792.SymptomTrackingBloc>(
        () => _i792.SymptomTrackingBloc(gh<_i830.SymptomTrackingFacade>()));
    gh.factory<_i1051.AccountWatcherBloc>(
        () => _i1051.AccountWatcherBloc(gh<_i380.AccountManagementFacade>()));
    gh.factory<_i712.OnboardingFormBloc>(
        () => _i712.OnboardingFormBloc(gh<_i380.AccountManagementFacade>()));
    gh.factory<_i357.AccountManagementBloc>(
        () => _i357.AccountManagementBloc(gh<_i380.AccountManagementFacade>()));
    return this;
  }
}
