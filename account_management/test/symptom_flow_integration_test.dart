import 'package:test/test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:doso/doso.dart';
import 'package:account_management/domain/core/unit.dart';
import 'package:account_management/domain/facade/period_tracking_facade.dart';
import 'package:account_management/domain/facade/symptom_tracking_facade.dart';
import 'package:account_management/infrastructure/services/firestore_service.dart';
import 'package:account_management/infrastructure/services/period_data_service.dart';
import 'package:account_management/repository/symptom_tracking_repository.dart';
import 'package:account_management/domain/model/symptom_model.dart';

// Generate mocks
@GenerateMocks([
  FirestoreService,
  PeriodTrackingFacade,
  PeriodDataService,
])
import 'symptom_flow_integration_test.mocks.dart';

void main() {
  group('Symptom Flow Integration Tests', () {
    late SymptomTrackingRepository repository;
    late MockFirestoreService mockFirestoreService;
    late MockPeriodTrackingFacade mockPeriodTrackingFacade;
    late MockPeriodDataService mockPeriodDataService;

    setUp(() {
      mockFirestoreService = MockFirestoreService();
      mockPeriodTrackingFacade = MockPeriodTrackingFacade();
      mockPeriodDataService = MockPeriodDataService();

      repository = SymptomTrackingRepository(
        mockFirestoreService,
        mockPeriodTrackingFacade,
        mockPeriodDataService,
      );
    });

    test('should trigger period tracking when flow level is saved', () async {
      // Arrange
      final testDate = DateTime(2024, 1, 15);
      final flowLevel = 2;
      final symptoms = <SymptomModel>[];

      // Mock successful symptom saving
      when(mockFirestoreService.saveSymptomData(
        date: anyNamed('date'),
        symptomData: anyNamed('symptomData'),
      )).thenAnswer((_) async => const Do.success(unit));

      // Mock flow validation to return true (should mark as period date)
      when(mockPeriodDataService.shouldMarkAsPeriodDate(
        any,
        any,
      )).thenAnswer((_) async => true);

      // Mock successful period date selection
      when(mockPeriodTrackingFacade.selectPeriodDates(
        any,
        flowLevels: anyNamed('flowLevels'),
      )).thenAnswer((_) async => const Do.success(unit));

      // Act
      final result = await repository.saveSymptomData(
        date: testDate,
        symptoms: symptoms,
        painLevel: null,
        flowLevel: flowLevel,
      );

      // Assert
      expect(result.isSuccess, true);

      // Verify that symptom data was saved
      verify(mockFirestoreService.saveSymptomData(
        date: testDate,
        symptomData: any,
      )).called(1);

      // Verify that flow validation was called
      verify(mockPeriodDataService.shouldMarkAsPeriodDate(
        testDate,
        {testDate: flowLevel},
      )).called(1);

      // Verify that period tracking was triggered
      verify(mockPeriodTrackingFacade.selectPeriodDates(
        {testDate},
        flowLevels: {testDate: flowLevel},
      )).called(1);
    });

    test('should not trigger period tracking when flow validation fails',
        () async {
      // Arrange
      final testDate = DateTime(2024, 1, 15);
      final flowLevel = 1;
      final symptoms = <SymptomModel>[];

      // Mock successful symptom saving
      when(mockFirestoreService.saveSymptomData(
        date: anyNamed('date'),
        symptomData: anyNamed('symptomData'),
      )).thenAnswer((_) async => const Do.success(unit));

      // Mock flow validation to return false (should NOT mark as period date)
      when(mockPeriodDataService.shouldMarkAsPeriodDate(
        any,
        any,
      )).thenAnswer((_) async => false);

      // Act
      final result = await repository.saveSymptomData(
        date: testDate,
        symptoms: symptoms,
        painLevel: null,
        flowLevel: flowLevel,
      );

      // Assert
      expect(result.isSuccess, true);

      // Verify that symptom data was saved
      verify(mockFirestoreService.saveSymptomData(
        date: testDate,
        symptomData: any,
      )).called(1);

      // Verify that flow validation was called
      verify(mockPeriodDataService.shouldMarkAsPeriodDate(
        testDate,
        {testDate: flowLevel},
      )).called(1);

      // Verify that period tracking was NOT triggered
      verifyNever(mockPeriodTrackingFacade.selectPeriodDates(
        any,
        flowLevels: anyNamed('flowLevels'),
      ));
    });

    test('should handle case when no flow level is provided', () async {
      // Arrange
      final testDate = DateTime(2024, 1, 15);
      final symptoms = <SymptomModel>[];

      // Mock successful symptom saving
      when(mockFirestoreService.saveSymptomData(
        date: anyNamed('date'),
        symptomData: anyNamed('symptomData'),
      )).thenAnswer((_) async => const Do.success(unit));

      // Act
      final result = await repository.saveSymptomData(
        date: testDate,
        symptoms: symptoms,
        painLevel: 5,
        flowLevel: null, // No flow level
      );

      // Assert
      expect(result.isSuccess, true);

      // Verify that symptom data was saved
      verify(mockFirestoreService.saveSymptomData(
        date: testDate,
        symptomData: any,
      )).called(1);

      // Verify that flow validation was NOT called
      verifyNever(mockPeriodDataService.shouldMarkAsPeriodDate(any, any));

      // Verify that period tracking was NOT triggered
      verifyNever(mockPeriodTrackingFacade.selectPeriodDates(
        any,
        flowLevels: anyNamed('flowLevels'),
      ));
    });
  });
}
