import 'package:account_management/application/symptom_tracking_bloc/symptom_tracking_bloc.dart';
import 'package:account_management/application/symptom_watcher_bloc/symptom_watcher_bloc.dart';
import 'package:account_management/domain/model/symptom_model.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'dart:io';
import '../../custom_widgets/emoji_slider.dart';

class DailySymptomPage extends StatefulWidget {
  final String? scrollToSection; // 'pain', 'symptoms', or 'flow'

  const DailySymptomPage({
    this.scrollToSection,
    Key? key,
  }) : super(key: key);

  @override
  State<DailySymptomPage> createState() => _DailySymptomPageState();
}

class _DailySymptomPageState extends State<DailySymptomPage> {
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _painSectionKey = GlobalKey();
  final GlobalKey _symptomsSectionKey = GlobalKey();
  final GlobalKey _flowSectionKey = GlobalKey();

  // Symptom tracking state
  List<SymptomModel> _selectedSymptoms = [];
  int _painLevel = 0;
  int _flowLevel = 0;
  Map<String, List<SymptomModel>> _symptomsByCategory = {};

  List<String> emoji = [
    '\u{1F601}', // 😁
    '\u{1F642}', // 🙂
    '\u{1F610}', // 😐
    '\u{1F615}', // 😕
    '\u{1F641}', // 🙁
    '\u{1F61E}', // 😞
    '\u{1F613}', // 😓
    '\u{1F623}', // 😣
    '\u{1F616}', // 😖
    '\u{1F635}\u{200D}\u{1F4AB}', // 😵‍💫
    //crying face
    '\u{1F62D}', // 😭
  ];

  List<String> painDescriptions = [
    'No Pain',
    'Discomfort',
    'Very Mild',
    'Mild',
    'Moderate',
    'Significant',
    'High',
    'Very High',
    'Intense',
    'Unbearable',
    'Worst Pain',
  ];

  @override
  void initState() {
    super.initState();

    // Scroll to specific section if requested
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.scrollToSection != null) {
        _scrollToSection(widget.scrollToSection!);
      }
    });
  }

  void _scrollToSection(String section) {
    GlobalKey? targetKey;
    switch (section) {
      case 'pain':
        targetKey = _painSectionKey;
        break;
      case 'symptoms':
        targetKey = _symptomsSectionKey;
        break;
      case 'flow':
        targetKey = _flowSectionKey;
        break;
    }

    if (targetKey?.currentContext != null) {
      Scrollable.ensureVisible(
        targetKey!.currentContext!,
        duration: Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  bool _canEditSymptoms(DateTime date) {
    final today = DateTime.now();
    final dateOnly = DateTime(date.year, date.month, date.day);
    final todayOnly = DateTime(today.year, today.month, today.day);
    return dateOnly.isBefore(todayOnly) || dateOnly.isAtSameMomentAs(todayOnly);
  }

  bool _canGoToNextDay(DateTime selectedDate) {
    final today = DateTime.now();
    final nextDay = selectedDate.add(Duration(days: 1));
    final nextDayOnly = DateTime(nextDay.year, nextDay.month, nextDay.day);
    final todayOnly = DateTime(today.year, today.month, today.day);
    return nextDayOnly.isBefore(todayOnly) ||
        nextDayOnly.isAtSameMomentAs(todayOnly);
  }

  void _changeDate(int days, DateTime currentDate) {
    final newDate = currentDate.add(Duration(days: days));

    // Use the bloc to change the date
    context.read<SymptomTrackingBloc>().add(
          SymptomTrackingEvent.dateChanged(newDate: newDate),
        );
  }

  void _saveSymptoms(DateTime selectedDate) {
    context.read<SymptomTrackingBloc>().add(
          SymptomTrackingEvent.saveSymptomData(
            date: selectedDate,
            symptoms: _selectedSymptoms.isNotEmpty ? _selectedSymptoms : null,
            painLevel: _painLevel > 0 ? _painLevel : null,
            flowLevel: _flowLevel > 0 ? _flowLevel : null,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<SymptomTrackingBloc, SymptomTrackingState>(
      listener: (context, state) {
        state.when(
          initial: (selectedDate) {},
          loading: (selectedDate) {},
          loaded: (selectedDate, symptoms, painLevel, flowLevel) {
            // Update UI state with loaded data
            setState(() {
              _selectedSymptoms = List<SymptomModel>.from(symptoms ?? []);
              _painLevel = painLevel ?? 0;
              _flowLevel = flowLevel ?? 0;
            });
          },
          success: (selectedDate) {
            Fluttertoast.showToast(
              msg: 'Symptoms saved successfully!',
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.BOTTOM,
            );
            Navigator.of(context).pop();
          },
          failure: (selectedDate, failure) {
            Fluttertoast.showToast(
              msg: 'Failed to save symptoms',
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.BOTTOM,
            );
          },
        );
      },
      child: BlocBuilder<SymptomTrackingBloc, SymptomTrackingState>(
        builder: (context, state) {
          final selectedDate = state.when(
            initial: (date) => date ?? DateTime.now(),
            loading: (date) => date ?? DateTime.now(),
            loaded: (date, _, __, ___) => date,
            success: (date) => date,
            failure: (date, _) => date ?? DateTime.now(),
          );

          return Scaffold(
            backgroundColor: Colors.transparent,
            bottomNavigationBar: Container(
              decoration: BoxDecoration(
                color: Color(0xffF8EEFF),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(32),
                  bottomRight: Radius.circular(32),
                ),
              ),
              child: Padding(
                padding: EdgeInsets.all(25),
                child: SizedBox(
                  width: double.infinity,
                  height: 80.h,
                  child: ElevatedButton(
                    onPressed: _canEditSymptoms(selectedDate)
                        ? () => _saveSymptoms(selectedDate)
                        : null,
                    style: ButtonStyle(
                      backgroundColor: WidgetStatePropertyAll(
                        _canEditSymptoms(selectedDate)
                            ? AppTheme.primaryColor
                            : Colors.grey,
                      ),
                      shape: WidgetStatePropertyAll(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                      ),
                    ),
                    child:
                        BlocBuilder<SymptomTrackingBloc, SymptomTrackingState>(
                      builder: (context, state) {
                        return state.when(
                          initial: (date) => Text(
                            _canEditSymptoms(selectedDate)
                                ? 'Save Symptoms'
                                : 'Cannot edit future dates',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          loading: (date) => SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          loaded: (date, _, __, ___) => Text(
                            _canEditSymptoms(selectedDate)
                                ? 'Save Symptoms'
                                : 'Cannot edit future dates',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          success: (date) => Text(
                            'Save Symptoms',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          failure: (date, _) => Text(
                            'Save Symptoms',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
            body: Container(
              height: MediaQuery.of(context).size.height * 0.9,
              decoration: BoxDecoration(
                //gradient background
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    // Light cream
                    Color(0xffF8EEFF),
                    Color(0xffF8EEFF), // Light purple
                  ],
                ),

                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(32),
                  topRight: Radius.circular(32),
                ),
              ),
              child: Column(
                children: [
                  // Handle bar
                  Container(
                    margin: EdgeInsets.only(top: 12.h),
                    width: 40.w,
                    height: 4.h,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(32),
                    ),
                  ),

                  // Header with date navigation
                  Padding(
                    padding: EdgeInsets.all(24.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Previous day button
                        IconButton(
                          onPressed: () => _changeDate(-1, selectedDate),
                          icon: Icon(
                            Icons.arrow_back_ios,
                            color: Color.fromRGBO(58, 38, 101, 1.0),
                            size: 20,
                          ),
                        ),
                        // Date display
                        Expanded(
                          child: Center(
                            child: Text(
                              DateFormat('EEEE, MMMM d, y')
                                  .format(selectedDate),
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                        // Next day button
                        IconButton(
                          onPressed: _canGoToNextDay(selectedDate)
                              ? () => _changeDate(1, selectedDate)
                              : null,
                          icon: Icon(
                            Icons.arrow_forward_ios,
                            color: _canGoToNextDay(selectedDate)
                                ? Color.fromRGBO(58, 38, 101, 1.0)
                                : Colors.grey,
                            size: 20,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Content
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: ListView(
                        controller: _scrollController,
                        shrinkWrap: true,
                        children: [
                          SizedBox(height: 20),
                          // Pain Level Section - Exact copy from original
                          Padding(
                              padding: EdgeInsets.symmetric(horizontal: 20.w),
                              child: Container(
                                key: _painSectionKey,
                                margin: EdgeInsets.zero,
                                height: 0.65.sw,
                                width: 0.90.sw,
                                decoration: BoxDecoration(
                                  boxShadow: [
                                    BoxShadow(
                                      color: Color(0x40000000),
                                      blurRadius: 4.0,
                                      offset: Offset(0, 1),
                                    ),
                                  ],
                                  borderRadius: BorderRadius.circular(32),
                                  color: Colors.white,
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      SizedBox(height: 20),
                                      Align(
                                          alignment: Alignment.centerLeft,
                                          child: Text(
                                            "What is Your Overall Pain?",
                                            style: GoogleFonts.poppins(
                                              color: Color.fromRGBO(
                                                  58, 38, 101, 1.0),
                                              fontWeight: FontWeight.w400,
                                              fontSize: 22,
                                            ),
                                          )),
                                      SizedBox(height: 30),
                                      Text(
                                        _painLevel.toString(),
                                        style: TextStyle(
                                          color:
                                              Color.fromRGBO(58, 38, 101, 1.0),
                                          fontWeight: FontWeight.w700,
                                          fontSize: 31,
                                        ),
                                      ),
                                      SizedBox(height: 10),
                                      Container(
                                        height: 77,
                                        margin: EdgeInsets.zero,
                                        child: Align(
                                          child: EmojiSlider(
                                            key: Key('emoji_slider'),
                                            currentValue: _painLevel.toDouble(),
                                            emojis: emoji,
                                            minValue: 0,
                                            maxValue: 10,
                                            labels: painDescriptions,
                                            onChanged:
                                                _canEditSymptoms(selectedDate)
                                                    ? (handlerIndex, lowerValue,
                                                        upperValue) {
                                                        setState(() {
                                                          _painLevel =
                                                              (lowerValue
                                                                      as double)
                                                                  .toInt();
                                                        });
                                                      }
                                                    : null,
                                          ),
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.all(0),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              'No Pain',
                                              style: TextStyle(
                                                  color: Colors.black),
                                            ),
                                            Text(
                                              'Worst Pain',
                                              style: TextStyle(
                                                  color: Colors.black),
                                            ),
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              )),
                          SizedBox(height: 20),

                          // Flow Section - Updated to match other symptoms layout
                          _buildFlowSection(selectedDate),
                          // Symptoms Section - New categorized design
                          Padding(
                              padding: EdgeInsets.symmetric(horizontal: 20.w),
                              child: BlocBuilder<SymptomWatcherBloc,
                                  SymptomWatcherState>(
                                builder: (context, symptomState) {
                                  return symptomState.when(
                                    initial: () => _buildSymptomsPlaceholder(),
                                    loading: () => _buildSymptomsLoading(),
                                    loadSuccess: (symptomsByCategory) {
                                      _symptomsByCategory = symptomsByCategory;
                                      return _buildSymptomsByCategory(
                                          selectedDate, symptomsByCategory);
                                    },
                                    loadFailure: (failure) =>
                                        _buildSymptomsError(),
                                  );
                                },
                              )),
                          SizedBox(height: 20),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildFlowSection(DateTime selectedDate) {
    final flowColor = Color(0xFFE05159); // #E05159

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Container(
        key: _flowSectionKey,
        height: .51.sw,
        margin: EdgeInsets.only(bottom: 20),
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Color(0x40000000),
              blurRadius: 4.0,
              offset: Offset(0, 1),
            ),
          ],
          borderRadius: BorderRadius.circular(32),
          color: Colors.white,
          border: Border.all(
            color: flowColor.withOpacity(0.3),
            width: 2,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Flow',
                style: TextStyle(
                  color: Color.fromRGBO(58, 38, 101, 1.0),
                  fontWeight: FontWeight.w700,
                  fontSize: 22,
                ),
              ),
              SizedBox(height: 15),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildFlowSymptomButton('Light', selectedDate, 1),
                  _buildFlowSymptomButton('Regular', selectedDate, 2),
                  _buildFlowSymptomButton('Heavy', selectedDate, 3),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFlowSymptomButton(
      String name, DateTime selectedDate, int level) {
    final isSelected = _flowLevel == level;
    final flowColor = Color(0xFFE05159); // #E05159

    return GestureDetector(
      onTap: _canEditSymptoms(selectedDate)
          ? () {
              setState(() {
                _flowLevel = isSelected ? 0 : level; // Toggle selection
              });
            }
          : null,
      child: Column(
        children: [
          Container(
            height: 67,
            width: 90,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: isSelected ? flowColor : Colors.white,
              border: Border.all(
                color: flowColor,
                width: 2,
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(8),
              child: _buildFlowIcon(level, isSelected, flowColor),
            ),
          ),
          SizedBox(height: 8),
          Text(
            name,
            style: TextStyle(
              color: Color.fromRGBO(58, 38, 101, 1.0),
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFlowIcon(int level, bool isSelected, Color flowColor) {
    final iconColor = isSelected ? Colors.white : flowColor;
    final assetPath = 'assets/home/<USER>';

    try {
      return SvgPicture.asset(
        assetPath,
        colorFilter: ColorFilter.mode(
          iconColor,
          BlendMode.srcIn,
        ),
        placeholderBuilder: (context) => Icon(
          Icons.water_drop,
          color: iconColor,
          size: 24,
        ),
      );
    } catch (e) {
      print('SVG loading error for flow icon: $e');
      return Icon(
        Icons.water_drop,
        color: iconColor,
        size: 24,
      );
    }
  }

  Widget _buildSymptomsPlaceholder() {
    return Container(
      key: _symptomsSectionKey,
      height: 200,
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Color(0x40000000),
            blurRadius: 4.0,
            offset: Offset(0, 1),
          ),
        ],
        borderRadius: BorderRadius.circular(32),
        color: Colors.white,
      ),
      child: Center(
        child: Text(
          'Loading symptoms...',
          style: TextStyle(
            color: Color.fromRGBO(58, 38, 101, 1.0),
            fontSize: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildSymptomsLoading() {
    return Container(
      key: _symptomsSectionKey,
      height: 200,
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Color(0x40000000),
            blurRadius: 4.0,
            offset: Offset(0, 1),
          ),
        ],
        borderRadius: BorderRadius.circular(32),
        color: Colors.white,
      ),
      child: Center(
        child: CircularProgressIndicator(
          color: Color.fromRGBO(58, 38, 101, 1.0),
        ),
      ),
    );
  }

  Widget _buildSymptomsError() {
    return Container(
      key: _symptomsSectionKey,
      height: 200,
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Color(0x40000000),
            blurRadius: 4.0,
            offset: Offset(0, 1),
          ),
        ],
        borderRadius: BorderRadius.circular(32),
        color: Colors.white,
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            SizedBox(height: 16),
            Text(
              'Failed to load symptoms',
              style: TextStyle(
                color: Color.fromRGBO(58, 38, 101, 1.0),
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSymptomsByCategory(DateTime selectedDate,
      Map<String, List<SymptomModel>> symptomsByCategory) {
    if (symptomsByCategory.isEmpty) {
      return _buildSymptomsPlaceholder();
    }

    return Column(
      key: _symptomsSectionKey,
      children: symptomsByCategory.entries.map((entry) {
        final category = entry.value.first.category;
        final symptoms = entry.value;

        // Get category color from first symptom (all symptoms in category should have same color)
        final categoryColor =
            symptoms.isNotEmpty && symptoms.first.colorCode != null
                ? _parseColor(symptoms.first.colorCode!)
                : Color.fromRGBO(88, 66, 148, 1); // Default purple color

        return Container(
          height: .54.sw,
          margin: EdgeInsets.only(bottom: 20),
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Color(0x40000000),
                blurRadius: 4.0,
                offset: Offset(0, 1),
              ),
            ],
            borderRadius: BorderRadius.circular(32),
            color: Colors.white,
            border: Border.all(
              color: categoryColor.withOpacity(0.3),
              width: 2,
            ),
          ),
          child: Padding(
            padding: EdgeInsets.only(top: 20, bottom: 0, right: 20, left: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  category,
                  style: TextStyle(
                    color: Color.fromRGBO(58, 38, 101, 1.0),
                    fontWeight: FontWeight.w700,
                    fontSize: 22,
                  ),
                ),
                SizedBox(height: 15),
                SizedBox(
                  height: 130, // Fixed height for the scrollable area
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: symptoms.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: EdgeInsets.only(
                          left: index == 0 ? 0 : 4,
                          right: index == symptoms.length - 1 ? 0 : 4,
                        ),
                        child:
                            _buildSymptomButton(symptoms[index], selectedDate),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSymptomButton(SymptomModel symptom, DateTime selectedDate) {
    final isSelected = _selectedSymptoms.any((s) => s.name == symptom.name);

    // Get symptom color or use default
    final symptomColor = symptom.colorCode != null
        ? _parseColor(symptom.colorCode!)
        : Color.fromRGBO(88, 66, 148, 1);

    return GestureDetector(
      onTap: _canEditSymptoms(selectedDate)
          ? () {
              setState(() {
                if (isSelected) {
                  _selectedSymptoms.removeWhere((s) => s.name == symptom.name);
                } else {
                  _selectedSymptoms.add(symptom);
                }
              });
            }
          : null,
      child: Column(
        children: [
          Container(
            height: 80,
            width: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: isSelected ? symptomColor : Colors.white,
              border: Border.all(
                color: symptomColor,
                width: 2,
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(8),
              child: _buildSymptomIcon(symptom, isSelected, symptomColor),
            ),
          ),
          SizedBox(height: 8),
          Container(
            width: 85,
            child: Text(
              symptom.name,
              style: TextStyle(
                color: Color.fromRGBO(58, 38, 101, 1.0),
                fontWeight: FontWeight.w500,
                fontSize: 13,
              ),
              textAlign: TextAlign.center,
            ),
          )
        ],
      ),
    );
  }

  Widget _buildSymptomIcon(SymptomModel symptom, bool isSelected,
      [Color? categoryColor]) {
    final iconColor = isSelected
        ? Colors.white
        : (categoryColor ?? Color.fromRGBO(88, 66, 148, 1));

    try {
      // Try to load from local cache first, then from URL
      if (symptom.localIconPath != null &&
          File(symptom.localIconPath!).existsSync()) {
        return SvgPicture.file(
          File(symptom.localIconPath!),
          colorFilter: ColorFilter.mode(
            iconColor,
            BlendMode.srcIn,
          ),
          placeholderBuilder: (context) => Icon(
            Icons.healing,
            color: iconColor,
            size: 24,
          ),
        );
      } else if (symptom.iconUrl != null && symptom.iconUrl!.isNotEmpty) {
        // Fallback to network image
        return SvgPicture.network(
          symptom.iconUrl!,
          colorFilter: ColorFilter.mode(
            iconColor,
            BlendMode.srcIn,
          ),
          placeholderBuilder: (context) => Icon(
            Icons.healing,
            color: iconColor,
            size: 24,
          ),
        );
      } else {
        // No icon available, show placeholder
        return Icon(
          Icons.healing,
          color: iconColor,
          size: 24,
        );
      }
    } catch (e) {
      print('SVG loading error for symptom ${symptom.name}: $e');
      return Icon(
        Icons.healing,
        color: iconColor,
        size: 24,
      );
    }
  }

  /// Helper method to parse color code string to Color object
  Color _parseColor(String colorCode) {
    try {
      // Remove # if present and ensure it's 6 characters
      String cleanColor = colorCode.replaceAll('#', '');
      if (cleanColor.length == 6) {
        return Color(int.parse('FF$cleanColor', radix: 16));
      }
      return Color.fromRGBO(88, 66, 148, 1); // Default purple if parsing fails
    } catch (e) {
      return Color.fromRGBO(88, 66, 148, 1); // Default purple if parsing fails
    }
  }
}
