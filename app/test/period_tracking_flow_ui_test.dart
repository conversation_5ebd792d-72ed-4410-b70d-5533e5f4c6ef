import 'package:flutter_test/flutter_test.dart';
import 'package:account_management/domain/model/period_tracking_model.dart';

void main() {
  group('Period Tracking Flow UI Logic Tests', () {
    test('should extract flow data correctly from period tracking models', () {
      // Create test data with flow levels
      final testData = <String, Map<String, PeriodTrackingModel>>{
        '2024_01': {
          '15': PeriodTrackingModel(
            date: DateTime(2024, 1, 15),
            flowLevel: 2, // Medium flow
            isPeriodDate: true,
          ),
          '16': PeriodTrackingModel(
            date: DateTime(2024, 1, 16),
            flowLevel: 3, // Heavy flow
            isPeriodDate: true,
          ),
          '17': PeriodTrackingModel(
            date: DateTime(2024, 1, 17),
            flowLevel: 1, // Light flow
            isPeriodDate: true,
          ),
          '18': PeriodTrackingModel(
            date: DateTime(2024, 1, 18),
            flowLevel: 0, // No flow (should not be included)
            isPeriodDate: true,
          ),
          '19': PeriodTrackingModel(
            date: DateTime(2024, 1, 19),
            isPeriodDate: true, // No flow level (should not be included)
          ),
        },
      };

      // Extract flow data (simulating the logic from the UI)
      final flowData = <DateTime, int>{};
      for (final monthEntry in testData.entries) {
        for (final dayEntry in monthEntry.value.entries) {
          final periodModel = dayEntry.value;
          
          // Extract flow data for dates that have flow levels
          if (periodModel.flowLevel != null && 
              periodModel.flowLevel! > 0 &&
              periodModel.date != null) {
            // Normalize the date to midnight for consistent comparison
            final normalizedDate = DateTime(
              periodModel.date!.year, 
              periodModel.date!.month, 
              periodModel.date!.day
            );
            flowData[normalizedDate] = periodModel.flowLevel!;
          }
        }
      }

      // Verify flow data extraction
      expect(flowData.length, equals(3)); // Only dates with flow > 0
      expect(flowData[DateTime(2024, 1, 15)], equals(2));
      expect(flowData[DateTime(2024, 1, 16)], equals(3));
      expect(flowData[DateTime(2024, 1, 17)], equals(1));
      expect(flowData.containsKey(DateTime(2024, 1, 18)), isFalse); // Flow = 0
      expect(flowData.containsKey(DateTime(2024, 1, 19)), isFalse); // No flow level
    });

    test('should identify dates with flow data correctly', () {
      final flowData = <DateTime, int>{
        DateTime(2024, 1, 15): 2,
        DateTime(2024, 1, 16): 3,
        DateTime(2024, 1, 17): 1,
      };

      // Test date normalization and lookup
      final testDate = DateTime(2024, 1, 15, 10, 30); // With time components
      final normalizedDate = DateTime(testDate.year, testDate.month, testDate.day);
      
      final hasFlowData = flowData.containsKey(normalizedDate);
      final flowLevel = flowData[normalizedDate];

      expect(hasFlowData, isTrue);
      expect(flowLevel, equals(2));
    });

    test('should handle dates without flow data', () {
      final flowData = <DateTime, int>{
        DateTime(2024, 1, 15): 2,
        DateTime(2024, 1, 16): 3,
      };

      final testDate = DateTime(2024, 1, 20); // Date not in flow data
      final hasFlowData = flowData.containsKey(testDate);
      
      expect(hasFlowData, isFalse);
    });

    test('should prioritize flow data over first/last period date logic', () {
      // This test verifies the UI logic priority:
      // 1. If date has flow data -> show circle
      // 2. Else if first period date -> show first date styling
      // 3. Else if last period date -> show last date styling
      // 4. Else -> show middle date styling

      final hasFlowData = true;
      final isFirstPeriodDate = true; // This should be ignored if hasFlowData is true
      final isFutureDate = false;

      // Simulate the UI logic
      String expectedStyling;
      if (hasFlowData && !isFutureDate) {
        expectedStyling = 'circle';
      } else if (isFirstPeriodDate) {
        expectedStyling = 'first';
      } else {
        expectedStyling = 'middle';
      }

      expect(expectedStyling, equals('circle'));
    });

    test('should not show circles for future dates even with flow data', () {
      final hasFlowData = true;
      final isFutureDate = true; // Future date

      // Simulate the UI logic
      String expectedStyling;
      if (hasFlowData && !isFutureDate) {
        expectedStyling = 'circle';
      } else {
        expectedStyling = 'other';
      }

      expect(expectedStyling, equals('other'));
    });
  });
}
