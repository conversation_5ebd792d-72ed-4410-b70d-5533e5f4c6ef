import 'package:bloc/bloc.dart';
import 'package:fpdart/fpdart.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:remote/domain/facade/remote_control_facade.dart';
import 'package:remote/domain/failures/remote_failures.dart';
import 'package:remote/domain/model/settings_prediction_response_model.dart';

part 'settings_application_event.dart';
part 'settings_application_state.dart';
part 'settings_application_bloc.freezed.dart';

@injectable
class SettingsApplicationBloc
    extends Bloc<SettingsApplicationEvent, SettingsApplicationState> {
  final RemoteControlFacade _remoteControlFacade;

  SettingsApplicationBloc(this._remoteControlFacade)
      : super(const SettingsApplicationState.initial()) {
    on<_ApplyPredictedSettings>(_onApplyPredictedSettings);
  }

  Future<void> _onApplyPredictedSettings(
    _ApplyPredictedSettings event,
    Emitter<SettingsApplicationState> emit,
  ) async {
    emit(const SettingsApplicationState.applyingSettings());

    try {
      final prediction = event.prediction;

      // First, apply the TENS mode (this should be done before setting level)
      final modeResult =
          await _remoteControlFacade.changeMode(prediction.recommendedTensMode);

      var hasError = false;
      modeResult.mapBoth(
        onLeft: (failure) {
          emit(SettingsApplicationState.settingsApplicationFailed(failure));
          hasError = true;
        },
        onRight: (_) {
          // Mode applied successfully
        },
      );

      if (hasError) return;

      // Wait a bit for mode to be applied
      await Future.delayed(const Duration(milliseconds: 500));

      // Apply TENS level - need to gradually increase to the target level
      for (int i = 0; i < prediction.recommendedTensLevel; i++) {
        final tensResult = await _remoteControlFacade.increaseTens();
        tensResult.mapBoth(
          onLeft: (failure) {
            emit(SettingsApplicationState.settingsApplicationFailed(failure));
            hasError = true;
          },
          onRight: (_) {
            // TENS level increased successfully
          },
        );

        if (hasError) return;

        // Small delay between increments
        await Future.delayed(const Duration(milliseconds: 200));
      }

      // Apply heat level - gradually increase to target
      for (int i = 0; i < prediction.recommendedHeatLevel; i++) {
        final heatResult = await _remoteControlFacade.increaseHeat();
        heatResult.mapBoth(
          onLeft: (failure) {
            emit(SettingsApplicationState.settingsApplicationFailed(failure));
            hasError = true;
          },
          onRight: (_) {
            // Heat level increased successfully
          },
        );

        if (hasError) return;

        // Small delay between increments
        await Future.delayed(const Duration(milliseconds: 200));
      }

      emit(const SettingsApplicationState.settingsApplied());
    } catch (e) {
      emit(SettingsApplicationState.settingsApplicationFailed(
          RemoteFailure.unexpectedFailure(
              'Failed to apply settings: ${e.toString()}')));
    }
  }
}
