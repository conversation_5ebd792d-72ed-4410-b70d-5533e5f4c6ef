// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'settings_application_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SettingsApplicationEvent {
  SettingsPredictionResponseModel get prediction;

  /// Create a copy of SettingsApplicationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SettingsApplicationEventCopyWith<SettingsApplicationEvent> get copyWith =>
      _$SettingsApplicationEventCopyWithImpl<SettingsApplicationEvent>(
          this as SettingsApplicationEvent, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SettingsApplicationEvent &&
            (identical(other.prediction, prediction) ||
                other.prediction == prediction));
  }

  @override
  int get hashCode => Object.hash(runtimeType, prediction);

  @override
  String toString() {
    return 'SettingsApplicationEvent(prediction: $prediction)';
  }
}

/// @nodoc
abstract mixin class $SettingsApplicationEventCopyWith<$Res> {
  factory $SettingsApplicationEventCopyWith(SettingsApplicationEvent value,
          $Res Function(SettingsApplicationEvent) _then) =
      _$SettingsApplicationEventCopyWithImpl;
  @useResult
  $Res call({SettingsPredictionResponseModel prediction});
}

/// @nodoc
class _$SettingsApplicationEventCopyWithImpl<$Res>
    implements $SettingsApplicationEventCopyWith<$Res> {
  _$SettingsApplicationEventCopyWithImpl(this._self, this._then);

  final SettingsApplicationEvent _self;
  final $Res Function(SettingsApplicationEvent) _then;

  /// Create a copy of SettingsApplicationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? prediction = null,
  }) {
    return _then(_self.copyWith(
      prediction: null == prediction
          ? _self.prediction
          : prediction // ignore: cast_nullable_to_non_nullable
              as SettingsPredictionResponseModel,
    ));
  }
}

/// Adds pattern-matching-related methods to [SettingsApplicationEvent].
extension SettingsApplicationEventPatterns on SettingsApplicationEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ApplyPredictedSettings value)? applyPredictedSettings,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ApplyPredictedSettings() when applyPredictedSettings != null:
        return applyPredictedSettings(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ApplyPredictedSettings value)
        applyPredictedSettings,
  }) {
    final _that = this;
    switch (_that) {
      case _ApplyPredictedSettings():
        return applyPredictedSettings(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ApplyPredictedSettings value)? applyPredictedSettings,
  }) {
    final _that = this;
    switch (_that) {
      case _ApplyPredictedSettings() when applyPredictedSettings != null:
        return applyPredictedSettings(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(SettingsPredictionResponseModel prediction)?
        applyPredictedSettings,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ApplyPredictedSettings() when applyPredictedSettings != null:
        return applyPredictedSettings(_that.prediction);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(SettingsPredictionResponseModel prediction)
        applyPredictedSettings,
  }) {
    final _that = this;
    switch (_that) {
      case _ApplyPredictedSettings():
        return applyPredictedSettings(_that.prediction);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(SettingsPredictionResponseModel prediction)?
        applyPredictedSettings,
  }) {
    final _that = this;
    switch (_that) {
      case _ApplyPredictedSettings() when applyPredictedSettings != null:
        return applyPredictedSettings(_that.prediction);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _ApplyPredictedSettings implements SettingsApplicationEvent {
  const _ApplyPredictedSettings(this.prediction);

  @override
  final SettingsPredictionResponseModel prediction;

  /// Create a copy of SettingsApplicationEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ApplyPredictedSettingsCopyWith<_ApplyPredictedSettings> get copyWith =>
      __$ApplyPredictedSettingsCopyWithImpl<_ApplyPredictedSettings>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ApplyPredictedSettings &&
            (identical(other.prediction, prediction) ||
                other.prediction == prediction));
  }

  @override
  int get hashCode => Object.hash(runtimeType, prediction);

  @override
  String toString() {
    return 'SettingsApplicationEvent.applyPredictedSettings(prediction: $prediction)';
  }
}

/// @nodoc
abstract mixin class _$ApplyPredictedSettingsCopyWith<$Res>
    implements $SettingsApplicationEventCopyWith<$Res> {
  factory _$ApplyPredictedSettingsCopyWith(_ApplyPredictedSettings value,
          $Res Function(_ApplyPredictedSettings) _then) =
      __$ApplyPredictedSettingsCopyWithImpl;
  @override
  @useResult
  $Res call({SettingsPredictionResponseModel prediction});
}

/// @nodoc
class __$ApplyPredictedSettingsCopyWithImpl<$Res>
    implements _$ApplyPredictedSettingsCopyWith<$Res> {
  __$ApplyPredictedSettingsCopyWithImpl(this._self, this._then);

  final _ApplyPredictedSettings _self;
  final $Res Function(_ApplyPredictedSettings) _then;

  /// Create a copy of SettingsApplicationEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? prediction = null,
  }) {
    return _then(_ApplyPredictedSettings(
      null == prediction
          ? _self.prediction
          : prediction // ignore: cast_nullable_to_non_nullable
              as SettingsPredictionResponseModel,
    ));
  }
}

/// @nodoc
mixin _$SettingsApplicationState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is SettingsApplicationState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SettingsApplicationState()';
  }
}

/// @nodoc
class $SettingsApplicationStateCopyWith<$Res> {
  $SettingsApplicationStateCopyWith(
      SettingsApplicationState _, $Res Function(SettingsApplicationState) __);
}

/// Adds pattern-matching-related methods to [SettingsApplicationState].
extension SettingsApplicationStatePatterns on SettingsApplicationState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_ApplyingSettings value)? applyingSettings,
    TResult Function(_SettingsApplied value)? settingsApplied,
    TResult Function(_SettingsApplicationFailed value)?
        settingsApplicationFailed,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _ApplyingSettings() when applyingSettings != null:
        return applyingSettings(_that);
      case _SettingsApplied() when settingsApplied != null:
        return settingsApplied(_that);
      case _SettingsApplicationFailed() when settingsApplicationFailed != null:
        return settingsApplicationFailed(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_ApplyingSettings value) applyingSettings,
    required TResult Function(_SettingsApplied value) settingsApplied,
    required TResult Function(_SettingsApplicationFailed value)
        settingsApplicationFailed,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that);
      case _ApplyingSettings():
        return applyingSettings(_that);
      case _SettingsApplied():
        return settingsApplied(_that);
      case _SettingsApplicationFailed():
        return settingsApplicationFailed(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_ApplyingSettings value)? applyingSettings,
    TResult? Function(_SettingsApplied value)? settingsApplied,
    TResult? Function(_SettingsApplicationFailed value)?
        settingsApplicationFailed,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _ApplyingSettings() when applyingSettings != null:
        return applyingSettings(_that);
      case _SettingsApplied() when settingsApplied != null:
        return settingsApplied(_that);
      case _SettingsApplicationFailed() when settingsApplicationFailed != null:
        return settingsApplicationFailed(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? applyingSettings,
    TResult Function()? settingsApplied,
    TResult Function(RemoteFailure failure)? settingsApplicationFailed,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _ApplyingSettings() when applyingSettings != null:
        return applyingSettings();
      case _SettingsApplied() when settingsApplied != null:
        return settingsApplied();
      case _SettingsApplicationFailed() when settingsApplicationFailed != null:
        return settingsApplicationFailed(_that.failure);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() applyingSettings,
    required TResult Function() settingsApplied,
    required TResult Function(RemoteFailure failure) settingsApplicationFailed,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial();
      case _ApplyingSettings():
        return applyingSettings();
      case _SettingsApplied():
        return settingsApplied();
      case _SettingsApplicationFailed():
        return settingsApplicationFailed(_that.failure);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? applyingSettings,
    TResult? Function()? settingsApplied,
    TResult? Function(RemoteFailure failure)? settingsApplicationFailed,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _ApplyingSettings() when applyingSettings != null:
        return applyingSettings();
      case _SettingsApplied() when settingsApplied != null:
        return settingsApplied();
      case _SettingsApplicationFailed() when settingsApplicationFailed != null:
        return settingsApplicationFailed(_that.failure);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Initial implements SettingsApplicationState {
  const _Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SettingsApplicationState.initial()';
  }
}

/// @nodoc

class _ApplyingSettings implements SettingsApplicationState {
  const _ApplyingSettings();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ApplyingSettings);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SettingsApplicationState.applyingSettings()';
  }
}

/// @nodoc

class _SettingsApplied implements SettingsApplicationState {
  const _SettingsApplied();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _SettingsApplied);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SettingsApplicationState.settingsApplied()';
  }
}

/// @nodoc

class _SettingsApplicationFailed implements SettingsApplicationState {
  const _SettingsApplicationFailed(this.failure);

  final RemoteFailure failure;

  /// Create a copy of SettingsApplicationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SettingsApplicationFailedCopyWith<_SettingsApplicationFailed>
      get copyWith =>
          __$SettingsApplicationFailedCopyWithImpl<_SettingsApplicationFailed>(
              this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SettingsApplicationFailed &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'SettingsApplicationState.settingsApplicationFailed(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class _$SettingsApplicationFailedCopyWith<$Res>
    implements $SettingsApplicationStateCopyWith<$Res> {
  factory _$SettingsApplicationFailedCopyWith(_SettingsApplicationFailed value,
          $Res Function(_SettingsApplicationFailed) _then) =
      __$SettingsApplicationFailedCopyWithImpl;
  @useResult
  $Res call({RemoteFailure failure});

  $RemoteFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$SettingsApplicationFailedCopyWithImpl<$Res>
    implements _$SettingsApplicationFailedCopyWith<$Res> {
  __$SettingsApplicationFailedCopyWithImpl(this._self, this._then);

  final _SettingsApplicationFailed _self;
  final $Res Function(_SettingsApplicationFailed) _then;

  /// Create a copy of SettingsApplicationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(_SettingsApplicationFailed(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as RemoteFailure,
    ));
  }

  /// Create a copy of SettingsApplicationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RemoteFailureCopyWith<$Res> get failure {
    return $RemoteFailureCopyWith<$Res>(_self.failure, (value) {
      return _then(_self.copyWith(failure: value));
    });
  }
}

// dart format on
