part of 'settings_application_bloc.dart';

@freezed
class SettingsApplicationState with _$SettingsApplicationState {
  const factory SettingsApplicationState.initial() = _Initial;
  const factory SettingsApplicationState.applyingSettings() = _ApplyingSettings;
  const factory SettingsApplicationState.settingsApplied() = _SettingsApplied;
  const factory SettingsApplicationState.settingsApplicationFailed(
    RemoteFailure failure,
  ) = _SettingsApplicationFailed;
}
