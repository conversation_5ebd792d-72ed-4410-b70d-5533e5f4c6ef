import 'package:bloc/bloc.dart';
import 'package:fpdart/fpdart.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:remote/domain/facade/settings_prediction_facade.dart';
import 'package:remote/domain/failures/settings_prediction_failure.dart';
import 'package:remote/domain/model/settings_prediction_request_model.dart';
import 'package:remote/domain/model/settings_prediction_response_model.dart';

part 'settings_prediction_event.dart';
part 'settings_prediction_state.dart';
part 'settings_prediction_bloc.freezed.dart';

@injectable
class SettingsPredictionBloc
    extends Bloc<SettingsPredictionEvent, SettingsPredictionState> {
  final SettingsPredictionFacade _settingsPredictionFacade;

  SettingsPredictionBloc(this._settingsPredictionFacade)
      : super(const SettingsPredictionState.initial()) {
    on<_PredictSettings>(_onPredictSettings);
    on<_PredictSettingsWithData>(_onPredictSettingsWithData);
  }

  Future<void> _onPredictSettings(
    _PredictSettings event,
    Emitter<SettingsPredictionState> emit,
  ) async {
    emit(const SettingsPredictionState.loading());

    // Use dummy data for now
    final dummyRequest = SettingsPredictionRequestModel.dummy();

    final failureOrSuccess =
        await _settingsPredictionFacade.predictSettings(dummyRequest);

    failureOrSuccess.mapBoth(
      onLeft: (failure) => emit(SettingsPredictionState.failure(failure)),
      onRight: (prediction) =>
          emit(SettingsPredictionState.success(prediction)),
    );
  }

  Future<void> _onPredictSettingsWithData(
    _PredictSettingsWithData event,
    Emitter<SettingsPredictionState> emit,
  ) async {
    emit(const SettingsPredictionState.loading());

    final failureOrSuccess =
        await _settingsPredictionFacade.predictSettings(event.request);

    failureOrSuccess.mapBoth(
      onLeft: (failure) => emit(SettingsPredictionState.failure(failure)),
      onRight: (prediction) =>
          emit(SettingsPredictionState.success(prediction)),
    );
  }
}
