// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'settings_prediction_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SettingsPredictionEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is SettingsPredictionEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SettingsPredictionEvent()';
  }
}

/// @nodoc
class $SettingsPredictionEventCopyWith<$Res> {
  $SettingsPredictionEventCopyWith(
      SettingsPredictionEvent _, $Res Function(SettingsPredictionEvent) __);
}

/// Adds pattern-matching-related methods to [SettingsPredictionEvent].
extension SettingsPredictionEventPatterns on SettingsPredictionEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_PredictSettings value)? predictSettings,
    TResult Function(_PredictSettingsWithData value)? predictSettingsWithData,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PredictSettings() when predictSettings != null:
        return predictSettings(_that);
      case _PredictSettingsWithData() when predictSettingsWithData != null:
        return predictSettingsWithData(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_PredictSettings value) predictSettings,
    required TResult Function(_PredictSettingsWithData value)
        predictSettingsWithData,
  }) {
    final _that = this;
    switch (_that) {
      case _PredictSettings():
        return predictSettings(_that);
      case _PredictSettingsWithData():
        return predictSettingsWithData(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_PredictSettings value)? predictSettings,
    TResult? Function(_PredictSettingsWithData value)? predictSettingsWithData,
  }) {
    final _that = this;
    switch (_that) {
      case _PredictSettings() when predictSettings != null:
        return predictSettings(_that);
      case _PredictSettingsWithData() when predictSettingsWithData != null:
        return predictSettingsWithData(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? predictSettings,
    TResult Function(SettingsPredictionRequestModel request)?
        predictSettingsWithData,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _PredictSettings() when predictSettings != null:
        return predictSettings();
      case _PredictSettingsWithData() when predictSettingsWithData != null:
        return predictSettingsWithData(_that.request);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() predictSettings,
    required TResult Function(SettingsPredictionRequestModel request)
        predictSettingsWithData,
  }) {
    final _that = this;
    switch (_that) {
      case _PredictSettings():
        return predictSettings();
      case _PredictSettingsWithData():
        return predictSettingsWithData(_that.request);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? predictSettings,
    TResult? Function(SettingsPredictionRequestModel request)?
        predictSettingsWithData,
  }) {
    final _that = this;
    switch (_that) {
      case _PredictSettings() when predictSettings != null:
        return predictSettings();
      case _PredictSettingsWithData() when predictSettingsWithData != null:
        return predictSettingsWithData(_that.request);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _PredictSettings implements SettingsPredictionEvent {
  const _PredictSettings();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _PredictSettings);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SettingsPredictionEvent.predictSettings()';
  }
}

/// @nodoc

class _PredictSettingsWithData implements SettingsPredictionEvent {
  const _PredictSettingsWithData(this.request);

  final SettingsPredictionRequestModel request;

  /// Create a copy of SettingsPredictionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PredictSettingsWithDataCopyWith<_PredictSettingsWithData> get copyWith =>
      __$PredictSettingsWithDataCopyWithImpl<_PredictSettingsWithData>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PredictSettingsWithData &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  @override
  String toString() {
    return 'SettingsPredictionEvent.predictSettingsWithData(request: $request)';
  }
}

/// @nodoc
abstract mixin class _$PredictSettingsWithDataCopyWith<$Res>
    implements $SettingsPredictionEventCopyWith<$Res> {
  factory _$PredictSettingsWithDataCopyWith(_PredictSettingsWithData value,
          $Res Function(_PredictSettingsWithData) _then) =
      __$PredictSettingsWithDataCopyWithImpl;
  @useResult
  $Res call({SettingsPredictionRequestModel request});
}

/// @nodoc
class __$PredictSettingsWithDataCopyWithImpl<$Res>
    implements _$PredictSettingsWithDataCopyWith<$Res> {
  __$PredictSettingsWithDataCopyWithImpl(this._self, this._then);

  final _PredictSettingsWithData _self;
  final $Res Function(_PredictSettingsWithData) _then;

  /// Create a copy of SettingsPredictionEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? request = null,
  }) {
    return _then(_PredictSettingsWithData(
      null == request
          ? _self.request
          : request // ignore: cast_nullable_to_non_nullable
              as SettingsPredictionRequestModel,
    ));
  }
}

/// @nodoc
mixin _$SettingsPredictionState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is SettingsPredictionState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SettingsPredictionState()';
  }
}

/// @nodoc
class $SettingsPredictionStateCopyWith<$Res> {
  $SettingsPredictionStateCopyWith(
      SettingsPredictionState _, $Res Function(SettingsPredictionState) __);
}

/// Adds pattern-matching-related methods to [SettingsPredictionState].
extension SettingsPredictionStatePatterns on SettingsPredictionState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Success value)? success,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _Success() when success != null:
        return success(_that);
      case _Failure() when failure != null:
        return failure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Success value) success,
    required TResult Function(_Failure value) failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that);
      case _Loading():
        return loading(_that);
      case _Success():
        return success(_that);
      case _Failure():
        return failure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Success value)? success,
    TResult? Function(_Failure value)? failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _Success() when success != null:
        return success(_that);
      case _Failure() when failure != null:
        return failure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(SettingsPredictionResponseModel prediction)? success,
    TResult Function(SettingsPredictionFailure failure)? failure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _Success() when success != null:
        return success(_that.prediction);
      case _Failure() when failure != null:
        return failure(_that.failure);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(SettingsPredictionResponseModel prediction)
        success,
    required TResult Function(SettingsPredictionFailure failure) failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial();
      case _Loading():
        return loading();
      case _Success():
        return success(_that.prediction);
      case _Failure():
        return failure(_that.failure);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(SettingsPredictionResponseModel prediction)? success,
    TResult? Function(SettingsPredictionFailure failure)? failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _Success() when success != null:
        return success(_that.prediction);
      case _Failure() when failure != null:
        return failure(_that.failure);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Initial implements SettingsPredictionState {
  const _Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SettingsPredictionState.initial()';
  }
}

/// @nodoc

class _Loading implements SettingsPredictionState {
  const _Loading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Loading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SettingsPredictionState.loading()';
  }
}

/// @nodoc

class _Success implements SettingsPredictionState {
  const _Success(this.prediction);

  final SettingsPredictionResponseModel prediction;

  /// Create a copy of SettingsPredictionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SuccessCopyWith<_Success> get copyWith =>
      __$SuccessCopyWithImpl<_Success>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Success &&
            (identical(other.prediction, prediction) ||
                other.prediction == prediction));
  }

  @override
  int get hashCode => Object.hash(runtimeType, prediction);

  @override
  String toString() {
    return 'SettingsPredictionState.success(prediction: $prediction)';
  }
}

/// @nodoc
abstract mixin class _$SuccessCopyWith<$Res>
    implements $SettingsPredictionStateCopyWith<$Res> {
  factory _$SuccessCopyWith(_Success value, $Res Function(_Success) _then) =
      __$SuccessCopyWithImpl;
  @useResult
  $Res call({SettingsPredictionResponseModel prediction});
}

/// @nodoc
class __$SuccessCopyWithImpl<$Res> implements _$SuccessCopyWith<$Res> {
  __$SuccessCopyWithImpl(this._self, this._then);

  final _Success _self;
  final $Res Function(_Success) _then;

  /// Create a copy of SettingsPredictionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? prediction = null,
  }) {
    return _then(_Success(
      null == prediction
          ? _self.prediction
          : prediction // ignore: cast_nullable_to_non_nullable
              as SettingsPredictionResponseModel,
    ));
  }
}

/// @nodoc

class _Failure implements SettingsPredictionState {
  const _Failure(this.failure);

  final SettingsPredictionFailure failure;

  /// Create a copy of SettingsPredictionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FailureCopyWith<_Failure> get copyWith =>
      __$FailureCopyWithImpl<_Failure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Failure &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'SettingsPredictionState.failure(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class _$FailureCopyWith<$Res>
    implements $SettingsPredictionStateCopyWith<$Res> {
  factory _$FailureCopyWith(_Failure value, $Res Function(_Failure) _then) =
      __$FailureCopyWithImpl;
  @useResult
  $Res call({SettingsPredictionFailure failure});

  $SettingsPredictionFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$FailureCopyWithImpl<$Res> implements _$FailureCopyWith<$Res> {
  __$FailureCopyWithImpl(this._self, this._then);

  final _Failure _self;
  final $Res Function(_Failure) _then;

  /// Create a copy of SettingsPredictionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(_Failure(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as SettingsPredictionFailure,
    ));
  }

  /// Create a copy of SettingsPredictionState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SettingsPredictionFailureCopyWith<$Res> get failure {
    return $SettingsPredictionFailureCopyWith<$Res>(_self.failure, (value) {
      return _then(_self.copyWith(failure: value));
    });
  }
}

// dart format on
