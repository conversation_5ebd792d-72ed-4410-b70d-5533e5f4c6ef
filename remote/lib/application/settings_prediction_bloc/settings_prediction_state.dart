part of 'settings_prediction_bloc.dart';

@freezed
class SettingsPredictionState with _$SettingsPredictionState {
  const factory SettingsPredictionState.initial() = _Initial;
  const factory SettingsPredictionState.loading() = _Loading;
  const factory SettingsPredictionState.success(
    SettingsPredictionResponseModel prediction,
  ) = _Success;
  const factory SettingsPredictionState.failure(
    SettingsPredictionFailure failure,
  ) = _Failure;
}
