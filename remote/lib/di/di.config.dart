// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:bluetooth/domain/facade/bluetooth_facade.dart' as _i909;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:remote/application/device_control_bloc/device_control_bloc.dart'
    as _i1050;
import 'package:remote/application/device_control_heat_bloc/device_control_heat_bloc.dart'
    as _i954;
import 'package:remote/application/device_control_heat_watcher_bloc/device_control_heat_watcher_bloc.dart'
    as _i695;
import 'package:remote/application/device_control_tens_bloc/device_control_tens_bloc.dart'
    as _i312;
import 'package:remote/application/device_control_tens_watcher_bloc/device_control_tens_watcher_bloc.dart'
    as _i812;
import 'package:remote/application/device_control_watcher_bloc/device_control_watcher_bloc.dart'
    as _i110;
import 'package:remote/application/device_status_watcher_bloc/device_status_watcher_bloc.dart'
    as _i47;
import 'package:remote/application/settings_application_bloc/settings_application_bloc.dart'
    as _i814;
import 'package:remote/application/settings_prediction_bloc/settings_prediction_bloc.dart'
    as _i855;
import 'package:remote/domain/facade/remote_control_facade.dart' as _i509;
import 'package:remote/domain/facade/settings_prediction_facade.dart' as _i441;
import 'package:remote/repository/remote_repository.dart' as _i1006;
import 'package:remote/repository/settings_prediction_repository.dart' as _i898;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    gh.lazySingleton<_i441.SettingsPredictionFacade>(
        () => _i898.SettingsPredictionRepository());
    gh.lazySingleton<_i509.RemoteControlFacade>(
        () => _i1006.RemoteRepository(gh<_i909.IBluetoothFacade>()));
    gh.factory<_i312.DeviceControlTensBloc>(
        () => _i312.DeviceControlTensBloc(gh<_i509.RemoteControlFacade>()));
    gh.factory<_i812.DeviceControlTensWatcherBloc>(() =>
        _i812.DeviceControlTensWatcherBloc(gh<_i509.RemoteControlFacade>()));
    gh.factory<_i110.DeviceControlWatcherBloc>(
        () => _i110.DeviceControlWatcherBloc(gh<_i509.RemoteControlFacade>()));
    gh.factory<_i954.DeviceControlHeatBloc>(
        () => _i954.DeviceControlHeatBloc(gh<_i509.RemoteControlFacade>()));
    gh.factory<_i1050.DeviceControlBloc>(
        () => _i1050.DeviceControlBloc(gh<_i509.RemoteControlFacade>()));
    gh.factory<_i695.DeviceControlHeatWatcherBloc>(() =>
        _i695.DeviceControlHeatWatcherBloc(gh<_i509.RemoteControlFacade>()));
    gh.factory<_i47.DeviceStatusWatcherBloc>(
        () => _i47.DeviceStatusWatcherBloc(gh<_i509.RemoteControlFacade>()));
    gh.factory<_i814.SettingsApplicationBloc>(
        () => _i814.SettingsApplicationBloc(gh<_i509.RemoteControlFacade>()));
    gh.factory<_i855.SettingsPredictionBloc>(() =>
        _i855.SettingsPredictionBloc(gh<_i441.SettingsPredictionFacade>()));
    return this;
  }
}
