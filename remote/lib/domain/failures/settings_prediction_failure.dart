import 'package:freezed_annotation/freezed_annotation.dart';

part 'settings_prediction_failure.freezed.dart';

@freezed
abstract class SettingsPredictionFailure with _$SettingsPredictionFailure {
  const factory SettingsPredictionFailure.serverError(String failureMessage) =
      ServerError;
  const factory SettingsPredictionFailure.noInternetConnection(
      String failureMessage) = NoInternetConnection;
  const factory SettingsPredictionFailure.invalidRequest(
      String failureMessage) = InvalidRequest;
  const factory SettingsPredictionFailure.apiTimeout(String failureMessage) =
      ApiTimeout;
  const factory SettingsPredictionFailure.unexpectedFailure(
      String failureMessage) = UnexpectedFailure;
}
