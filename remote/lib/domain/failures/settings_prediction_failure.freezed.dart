// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'settings_prediction_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SettingsPredictionFailure {
  String get failureMessage;

  /// Create a copy of SettingsPredictionFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SettingsPredictionFailureCopyWith<SettingsPredictionFailure> get copyWith =>
      _$SettingsPredictionFailureCopyWithImpl<SettingsPredictionFailure>(
          this as SettingsPredictionFailure, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SettingsPredictionFailure &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  @override
  String toString() {
    return 'SettingsPredictionFailure(failureMessage: $failureMessage)';
  }
}

/// @nodoc
abstract mixin class $SettingsPredictionFailureCopyWith<$Res> {
  factory $SettingsPredictionFailureCopyWith(SettingsPredictionFailure value,
          $Res Function(SettingsPredictionFailure) _then) =
      _$SettingsPredictionFailureCopyWithImpl;
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class _$SettingsPredictionFailureCopyWithImpl<$Res>
    implements $SettingsPredictionFailureCopyWith<$Res> {
  _$SettingsPredictionFailureCopyWithImpl(this._self, this._then);

  final SettingsPredictionFailure _self;
  final $Res Function(SettingsPredictionFailure) _then;

  /// Create a copy of SettingsPredictionFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(_self.copyWith(
      failureMessage: null == failureMessage
          ? _self.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [SettingsPredictionFailure].
extension SettingsPredictionFailurePatterns on SettingsPredictionFailure {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ServerError value)? serverError,
    TResult Function(NoInternetConnection value)? noInternetConnection,
    TResult Function(InvalidRequest value)? invalidRequest,
    TResult Function(ApiTimeout value)? apiTimeout,
    TResult Function(UnexpectedFailure value)? unexpectedFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case ServerError() when serverError != null:
        return serverError(_that);
      case NoInternetConnection() when noInternetConnection != null:
        return noInternetConnection(_that);
      case InvalidRequest() when invalidRequest != null:
        return invalidRequest(_that);
      case ApiTimeout() when apiTimeout != null:
        return apiTimeout(_that);
      case UnexpectedFailure() when unexpectedFailure != null:
        return unexpectedFailure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ServerError value) serverError,
    required TResult Function(NoInternetConnection value) noInternetConnection,
    required TResult Function(InvalidRequest value) invalidRequest,
    required TResult Function(ApiTimeout value) apiTimeout,
    required TResult Function(UnexpectedFailure value) unexpectedFailure,
  }) {
    final _that = this;
    switch (_that) {
      case ServerError():
        return serverError(_that);
      case NoInternetConnection():
        return noInternetConnection(_that);
      case InvalidRequest():
        return invalidRequest(_that);
      case ApiTimeout():
        return apiTimeout(_that);
      case UnexpectedFailure():
        return unexpectedFailure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ServerError value)? serverError,
    TResult? Function(NoInternetConnection value)? noInternetConnection,
    TResult? Function(InvalidRequest value)? invalidRequest,
    TResult? Function(ApiTimeout value)? apiTimeout,
    TResult? Function(UnexpectedFailure value)? unexpectedFailure,
  }) {
    final _that = this;
    switch (_that) {
      case ServerError() when serverError != null:
        return serverError(_that);
      case NoInternetConnection() when noInternetConnection != null:
        return noInternetConnection(_that);
      case InvalidRequest() when invalidRequest != null:
        return invalidRequest(_that);
      case ApiTimeout() when apiTimeout != null:
        return apiTimeout(_that);
      case UnexpectedFailure() when unexpectedFailure != null:
        return unexpectedFailure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failureMessage)? serverError,
    TResult Function(String failureMessage)? noInternetConnection,
    TResult Function(String failureMessage)? invalidRequest,
    TResult Function(String failureMessage)? apiTimeout,
    TResult Function(String failureMessage)? unexpectedFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case ServerError() when serverError != null:
        return serverError(_that.failureMessage);
      case NoInternetConnection() when noInternetConnection != null:
        return noInternetConnection(_that.failureMessage);
      case InvalidRequest() when invalidRequest != null:
        return invalidRequest(_that.failureMessage);
      case ApiTimeout() when apiTimeout != null:
        return apiTimeout(_that.failureMessage);
      case UnexpectedFailure() when unexpectedFailure != null:
        return unexpectedFailure(_that.failureMessage);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failureMessage) serverError,
    required TResult Function(String failureMessage) noInternetConnection,
    required TResult Function(String failureMessage) invalidRequest,
    required TResult Function(String failureMessage) apiTimeout,
    required TResult Function(String failureMessage) unexpectedFailure,
  }) {
    final _that = this;
    switch (_that) {
      case ServerError():
        return serverError(_that.failureMessage);
      case NoInternetConnection():
        return noInternetConnection(_that.failureMessage);
      case InvalidRequest():
        return invalidRequest(_that.failureMessage);
      case ApiTimeout():
        return apiTimeout(_that.failureMessage);
      case UnexpectedFailure():
        return unexpectedFailure(_that.failureMessage);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failureMessage)? serverError,
    TResult? Function(String failureMessage)? noInternetConnection,
    TResult? Function(String failureMessage)? invalidRequest,
    TResult? Function(String failureMessage)? apiTimeout,
    TResult? Function(String failureMessage)? unexpectedFailure,
  }) {
    final _that = this;
    switch (_that) {
      case ServerError() when serverError != null:
        return serverError(_that.failureMessage);
      case NoInternetConnection() when noInternetConnection != null:
        return noInternetConnection(_that.failureMessage);
      case InvalidRequest() when invalidRequest != null:
        return invalidRequest(_that.failureMessage);
      case ApiTimeout() when apiTimeout != null:
        return apiTimeout(_that.failureMessage);
      case UnexpectedFailure() when unexpectedFailure != null:
        return unexpectedFailure(_that.failureMessage);
      case _:
        return null;
    }
  }
}

/// @nodoc

class ServerError implements SettingsPredictionFailure {
  const ServerError(this.failureMessage);

  @override
  final String failureMessage;

  /// Create a copy of SettingsPredictionFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ServerErrorCopyWith<ServerError> get copyWith =>
      _$ServerErrorCopyWithImpl<ServerError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ServerError &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  @override
  String toString() {
    return 'SettingsPredictionFailure.serverError(failureMessage: $failureMessage)';
  }
}

/// @nodoc
abstract mixin class $ServerErrorCopyWith<$Res>
    implements $SettingsPredictionFailureCopyWith<$Res> {
  factory $ServerErrorCopyWith(
          ServerError value, $Res Function(ServerError) _then) =
      _$ServerErrorCopyWithImpl;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class _$ServerErrorCopyWithImpl<$Res> implements $ServerErrorCopyWith<$Res> {
  _$ServerErrorCopyWithImpl(this._self, this._then);

  final ServerError _self;
  final $Res Function(ServerError) _then;

  /// Create a copy of SettingsPredictionFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(ServerError(
      null == failureMessage
          ? _self.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class NoInternetConnection implements SettingsPredictionFailure {
  const NoInternetConnection(this.failureMessage);

  @override
  final String failureMessage;

  /// Create a copy of SettingsPredictionFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $NoInternetConnectionCopyWith<NoInternetConnection> get copyWith =>
      _$NoInternetConnectionCopyWithImpl<NoInternetConnection>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is NoInternetConnection &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  @override
  String toString() {
    return 'SettingsPredictionFailure.noInternetConnection(failureMessage: $failureMessage)';
  }
}

/// @nodoc
abstract mixin class $NoInternetConnectionCopyWith<$Res>
    implements $SettingsPredictionFailureCopyWith<$Res> {
  factory $NoInternetConnectionCopyWith(NoInternetConnection value,
          $Res Function(NoInternetConnection) _then) =
      _$NoInternetConnectionCopyWithImpl;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class _$NoInternetConnectionCopyWithImpl<$Res>
    implements $NoInternetConnectionCopyWith<$Res> {
  _$NoInternetConnectionCopyWithImpl(this._self, this._then);

  final NoInternetConnection _self;
  final $Res Function(NoInternetConnection) _then;

  /// Create a copy of SettingsPredictionFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(NoInternetConnection(
      null == failureMessage
          ? _self.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class InvalidRequest implements SettingsPredictionFailure {
  const InvalidRequest(this.failureMessage);

  @override
  final String failureMessage;

  /// Create a copy of SettingsPredictionFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $InvalidRequestCopyWith<InvalidRequest> get copyWith =>
      _$InvalidRequestCopyWithImpl<InvalidRequest>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is InvalidRequest &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  @override
  String toString() {
    return 'SettingsPredictionFailure.invalidRequest(failureMessage: $failureMessage)';
  }
}

/// @nodoc
abstract mixin class $InvalidRequestCopyWith<$Res>
    implements $SettingsPredictionFailureCopyWith<$Res> {
  factory $InvalidRequestCopyWith(
          InvalidRequest value, $Res Function(InvalidRequest) _then) =
      _$InvalidRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class _$InvalidRequestCopyWithImpl<$Res>
    implements $InvalidRequestCopyWith<$Res> {
  _$InvalidRequestCopyWithImpl(this._self, this._then);

  final InvalidRequest _self;
  final $Res Function(InvalidRequest) _then;

  /// Create a copy of SettingsPredictionFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(InvalidRequest(
      null == failureMessage
          ? _self.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class ApiTimeout implements SettingsPredictionFailure {
  const ApiTimeout(this.failureMessage);

  @override
  final String failureMessage;

  /// Create a copy of SettingsPredictionFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ApiTimeoutCopyWith<ApiTimeout> get copyWith =>
      _$ApiTimeoutCopyWithImpl<ApiTimeout>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ApiTimeout &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  @override
  String toString() {
    return 'SettingsPredictionFailure.apiTimeout(failureMessage: $failureMessage)';
  }
}

/// @nodoc
abstract mixin class $ApiTimeoutCopyWith<$Res>
    implements $SettingsPredictionFailureCopyWith<$Res> {
  factory $ApiTimeoutCopyWith(
          ApiTimeout value, $Res Function(ApiTimeout) _then) =
      _$ApiTimeoutCopyWithImpl;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class _$ApiTimeoutCopyWithImpl<$Res> implements $ApiTimeoutCopyWith<$Res> {
  _$ApiTimeoutCopyWithImpl(this._self, this._then);

  final ApiTimeout _self;
  final $Res Function(ApiTimeout) _then;

  /// Create a copy of SettingsPredictionFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(ApiTimeout(
      null == failureMessage
          ? _self.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class UnexpectedFailure implements SettingsPredictionFailure {
  const UnexpectedFailure(this.failureMessage);

  @override
  final String failureMessage;

  /// Create a copy of SettingsPredictionFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UnexpectedFailureCopyWith<UnexpectedFailure> get copyWith =>
      _$UnexpectedFailureCopyWithImpl<UnexpectedFailure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UnexpectedFailure &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  @override
  String toString() {
    return 'SettingsPredictionFailure.unexpectedFailure(failureMessage: $failureMessage)';
  }
}

/// @nodoc
abstract mixin class $UnexpectedFailureCopyWith<$Res>
    implements $SettingsPredictionFailureCopyWith<$Res> {
  factory $UnexpectedFailureCopyWith(
          UnexpectedFailure value, $Res Function(UnexpectedFailure) _then) =
      _$UnexpectedFailureCopyWithImpl;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class _$UnexpectedFailureCopyWithImpl<$Res>
    implements $UnexpectedFailureCopyWith<$Res> {
  _$UnexpectedFailureCopyWithImpl(this._self, this._then);

  final UnexpectedFailure _self;
  final $Res Function(UnexpectedFailure) _then;

  /// Create a copy of SettingsPredictionFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(UnexpectedFailure(
      null == failureMessage
          ? _self.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
