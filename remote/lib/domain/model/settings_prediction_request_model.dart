import 'package:json_annotation/json_annotation.dart';

part 'settings_prediction_request_model.g.dart';

@JsonSerializable()
class SettingsPredictionRequestModel {
  @Json<PERSON>ey(name: 'user_age')
  final int userAge;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_cycle_length')
  final int userCycleLength;

  @Json<PERSON>ey(name: 'user_period_length')
  final int userPeriodLength;

  @<PERSON>sonKey(name: 'is_period_day')
  final bool isPeriodDay;

  @<PERSON>sonKey(name: 'is_ovulation_day')
  final bool isOvulationDay;

  @<PERSON>sonKey(name: 'current_pain_level')
  final int currentPainLevel;

  @<PERSON>sonKey(name: 'current_flow_level')
  final int currentFlowLevel;

  @JsonKey(name: 'has_medications')
  final bool hasMedications;

  @<PERSON>sonKey(name: 'medication_count')
  final int medicationCount;

  @<PERSON>sonKey(name: 'user_experience')
  final String userExperience;

  @<PERSON>sonKey(name: 'time_of_day')
  final String timeOfDay;

  @<PERSON>son<PERSON><PERSON>(name: 'previous_tens_level')
  final int previousTensLevel;

  @Json<PERSON>ey(name: 'tens_mode')
  final String tensMode;

  const SettingsPredictionRequestModel({
    required this.userAge,
    required this.userCycleLength,
    required this.userPeriodLength,
    required this.isPeriodDay,
    required this.isOvulationDay,
    required this.currentPainLevel,
    required this.currentFlowLevel,
    required this.hasMedications,
    required this.medicationCount,
    required this.userExperience,
    required this.timeOfDay,
    required this.previousTensLevel,
    required this.tensMode,
  });

  factory SettingsPredictionRequestModel.fromJson(Map<String, dynamic> json) =>
      _$SettingsPredictionRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$SettingsPredictionRequestModelToJson(this);

  // Factory constructor for dummy/default data
  factory SettingsPredictionRequestModel.dummy() =>
      const SettingsPredictionRequestModel(
        userAge: 28,
        userCycleLength: 30,
        userPeriodLength: 5,
        isPeriodDay: true,
        isOvulationDay: false,
        currentPainLevel: 8,
        currentFlowLevel: 3,
        hasMedications: true,
        medicationCount: 2,
        userExperience: "experienced_user",
        timeOfDay: "afternoon",
        previousTensLevel: 5,
        tensMode: "continuous",
      );
}
