// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'settings_prediction_request_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SettingsPredictionRequestModel _$SettingsPredictionRequestModelFromJson(
        Map<String, dynamic> json) =>
    SettingsPredictionRequestModel(
      userAge: (json['user_age'] as num).toInt(),
      userCycleLength: (json['user_cycle_length'] as num).toInt(),
      userPeriodLength: (json['user_period_length'] as num).toInt(),
      isPeriodDay: json['is_period_day'] as bool,
      isOvulationDay: json['is_ovulation_day'] as bool,
      currentPainLevel: (json['current_pain_level'] as num).toInt(),
      currentFlowLevel: (json['current_flow_level'] as num).toInt(),
      hasMedications: json['has_medications'] as bool,
      medicationCount: (json['medication_count'] as num).toInt(),
      userExperience: json['user_experience'] as String,
      timeOfDay: json['time_of_day'] as String,
      previousTensLevel: (json['previous_tens_level'] as num).toInt(),
      tensMode: json['tens_mode'] as String,
    );

Map<String, dynamic> _$SettingsPredictionRequestModelToJson(
        SettingsPredictionRequestModel instance) =>
    <String, dynamic>{
      'user_age': instance.userAge,
      'user_cycle_length': instance.userCycleLength,
      'user_period_length': instance.userPeriodLength,
      'is_period_day': instance.isPeriodDay,
      'is_ovulation_day': instance.isOvulationDay,
      'current_pain_level': instance.currentPainLevel,
      'current_flow_level': instance.currentFlowLevel,
      'has_medications': instance.hasMedications,
      'medication_count': instance.medicationCount,
      'user_experience': instance.userExperience,
      'time_of_day': instance.timeOfDay,
      'previous_tens_level': instance.previousTensLevel,
      'tens_mode': instance.tensMode,
    };
