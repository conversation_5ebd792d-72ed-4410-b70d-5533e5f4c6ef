import 'package:json_annotation/json_annotation.dart';

part 'settings_prediction_response_model.g.dart';

@JsonSerializable()
class SettingsPredictionResponseModel {
  @JsonKey(name: 'recommended_tens_level')
  final int recommendedTensLevel;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'recommended_tens_mode')
  final int recommendedTensMode;

  @Json<PERSON>ey(name: 'recommended_heat_level')
  final int recommendedHeatLevel;

  @JsonKey(name: 'confidence_score')
  final double confidenceScore;

  @Json<PERSON>ey(name: 'additional_guidance')
  final String additionalGuidance;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'recommendation_explanation')
  final String recommendationExplanation;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'prediction_timestamp')
  final String predictionTimestamp;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'model_version')
  final String modelVersion;

  const SettingsPredictionResponseModel({
    required this.recommendedTensLevel,
    required this.recommendedTensMode,
    required this.recommendedHeatLevel,
    required this.confidenceScore,
    required this.additionalGuidance,
    required this.recommendationExplanation,
    required this.predictionTimestamp,
    required this.modelVersion,
  });

  factory SettingsPredictionResponseModel.fromJson(Map<String, dynamic> json) =>
      _$SettingsPredictionResponseModelFromJson(json);

  Map<String, dynamic> toJson() =>
      _$SettingsPredictionResponseModelToJson(this);

  // Helper method to get mode name for UI display
  String get tensModeName {
    switch (recommendedTensMode) {
      case 1:
        return 'Pulse';
      case 2:
        return 'Constant';
      case 3:
        return 'Burst';
      default:
        return 'Unknown';
    }
  }
}
