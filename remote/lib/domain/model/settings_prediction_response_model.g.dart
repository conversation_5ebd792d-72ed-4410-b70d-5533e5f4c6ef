// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'settings_prediction_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SettingsPredictionResponseModel _$SettingsPredictionResponseModelFromJson(
        Map<String, dynamic> json) =>
    SettingsPredictionResponseModel(
      recommendedTensLevel: (json['recommended_tens_level'] as num).toInt(),
      recommendedTensMode: (json['recommended_tens_mode'] as num).toInt(),
      recommendedHeatLevel: (json['recommended_heat_level'] as num).toInt(),
      confidenceScore: (json['confidence_score'] as num).toDouble(),
      additionalGuidance: json['additional_guidance'] as String,
      recommendationExplanation: json['recommendation_explanation'] as String,
      predictionTimestamp: json['prediction_timestamp'] as String,
      modelVersion: json['model_version'] as String,
    );

Map<String, dynamic> _$SettingsPredictionResponseModelToJson(
        SettingsPredictionResponseModel instance) =>
    <String, dynamic>{
      'recommended_tens_level': instance.recommendedTensLevel,
      'recommended_tens_mode': instance.recommendedTensMode,
      'recommended_heat_level': instance.recommendedHeatLevel,
      'confidence_score': instance.confidenceScore,
      'additional_guidance': instance.additionalGuidance,
      'recommendation_explanation': instance.recommendationExplanation,
      'prediction_timestamp': instance.predictionTimestamp,
      'model_version': instance.modelVersion,
    };
