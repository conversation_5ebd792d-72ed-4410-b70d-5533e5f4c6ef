import 'dart:convert';
import 'dart:io';
import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:http/http.dart' as http;
import 'package:remote/domain/facade/settings_prediction_facade.dart';
import 'package:remote/domain/failures/settings_prediction_failure.dart';
import 'package:remote/domain/model/settings_prediction_request_model.dart';
import 'package:remote/domain/model/settings_prediction_response_model.dart';

@LazySingleton(as: SettingsPredictionFacade)
class SettingsPredictionRepository implements SettingsPredictionFacade {
  static const String _baseUrl =
      'https://predict-tens-level-tsa7y454ua-uc.a.run.app';
  static const Duration _timeout = Duration(seconds: 30);

  @override
  Future<Either<SettingsPredictionFailure, SettingsPredictionResponseModel>>
      predictSettings(SettingsPredictionRequestModel request) async {
    try {
      final response = await http
          .post(
            Uri.parse(_baseUrl),
            headers: {
              'Content-Type': 'application/json',
            },
            body: jsonEncode(request.toJson()),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final responseBody = jsonDecode(response.body) as Map<String, dynamic>;
        final predictionResponse =
            SettingsPredictionResponseModel.fromJson(responseBody);
        return Right(predictionResponse);
      } else if (response.statusCode >= 400 && response.statusCode < 500) {
        return Left(SettingsPredictionFailure.invalidRequest(
          'Invalid request: ${response.statusCode} - ${response.body}',
        ));
      } else if (response.statusCode >= 500) {
        return Left(SettingsPredictionFailure.serverError(
          'Server error: ${response.statusCode} - ${response.body}',
        ));
      } else {
        return Left(SettingsPredictionFailure.unexpectedFailure(
          'Unexpected response: ${response.statusCode}',
        ));
      }
    } on SocketException {
      return const Left(SettingsPredictionFailure.noInternetConnection(
        'No internet connection available',
      ));
    } on HttpException catch (e) {
      return Left(SettingsPredictionFailure.serverError(
        'HTTP error: ${e.message}',
      ));
    } on FormatException catch (e) {
      return Left(SettingsPredictionFailure.unexpectedFailure(
        'Invalid response format: ${e.message}',
      ));
    } catch (e) {
      if (e.toString().contains('TimeoutException')) {
        return const Left(SettingsPredictionFailure.apiTimeout(
          'Request timeout after 30 seconds',
        ));
      }
      return Left(SettingsPredictionFailure.unexpectedFailure(
        'Unexpected error: ${e.toString()}',
      ));
    }
  }
}
